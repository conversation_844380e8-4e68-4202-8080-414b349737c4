<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoFeedBackMaterialInfoMapper">


    <select id="selectMaterialInfoListByCondition"
            resultType="com.ruoyi.system.api.domain.vo.order.MaterialInfoListVO">
        SELECT
            ovfbmi.id,
            ov.id AS video_id,
            ov.rollback_id,
            ov.product_pic,
            ov.video_code,
            ov.product_chinese,
            ov.product_english,
            ov.product_link,
            ov.platform,
            ov.model_type,
            ov.shooting_country,
            ov.video_format,
            ov.pic_count,
            ov.reference_pic AS reference_pic_id,
            ov.refund_pic_count,
            ov.`status`,
            ovmc.model_id AS shoot_model_id,
            ov.contact_id,
            ov.issue_id,
            IFNULL( hcr.history_clip_record, 0 ) AS history_clip_record,
            ovfbmi.link,
            ov.reference_video_link,
            ov.video_duration,
            ov.create_order_user_id,
            ovfbmi.get_status,
            ovfbmi.get_code,
            ovfbmi.get_by_id,
            ovfbmi.get_time,
            ovfbmi.edit_by_id,
            ovfbmi.edit_time,
            ovfbmi.feedback_by_id,
            ovfbmi.feedback_time,
            MAX(ovfbmi.status_time) OVER (PARTITION BY ov.id) AS max_status_time,
            ovtd.work_order_type
        FROM
            order_video_feed_back_material_info ovfbmi
                JOIN order_video_feed_back_material ovfbm ON ovfbm.id = ovfbmi.material_id
                JOIN order_video ov ON ov.id = ovfbm.video_id
                LEFT JOIN order_video_task_detail ovtd ON ovtd.id = ovfbmi.task_detail_id
                LEFT JOIN (
                        SELECT
                            a.video_id, a.rollback_id, a.model_id
                        FROM
                            order_video_model_change a
                                LEFT JOIN order_video_model_change b
                                    ON a.video_id = b.video_id
                                    AND IFNULL(a.rollback_id, 0) = IFNULL(b.rollback_id, 0)
                                    AND a.source = ${@com.ruoyi.common.core.enums.OrderVideoModelChangeSourceEnum@SHOOT_MODEL.getCode}
                                    AND b.source = ${@com.ruoyi.common.core.enums.OrderVideoModelChangeSourceEnum@SHOOT_MODEL.getCode}
                                    AND b.selected_time > a.selected_time
                        WHERE
                            b.video_id IS NULL
                            AND a.source = ${@com.ruoyi.common.core.enums.OrderVideoModelChangeSourceEnum@SHOOT_MODEL.getCode}
                ) ovmc ON ovmc.video_id = ovfbm.video_id AND IFNULL( ovmc.rollback_id, 0 ) = IFNULL( ovfbm.rollback_id, 0 )
                LEFT JOIN (
                    SELECT
                        m.video_id,
                        COUNT(*) AS history_clip_record
                    FROM
                        order_video_feed_back_material_info t
                            JOIN order_video_feed_back_material m ON m.id = t.material_id
                    GROUP BY
                        m.video_id
                ) hcr ON hcr.video_id = ov.id
        <where>
            ovfbmi.create_time >= #{oldDataEndTime}

            <if test="dto.status != null">
                AND ovfbmi.status = #{dto.status}
            </if>

            <if test="dto.keyword != null and dto.keyword != '' ">
                AND (
                    ov.video_code LIKE CONCAT('%', #{dto.keyword}, '%')
                    OR ov.product_chinese LIKE CONCAT('%', #{dto.keyword}, '%')
                    OR ov.product_english LIKE CONCAT('%', #{dto.keyword}, '%')
                    OR ov.product_link LIKE CONCAT('%', #{dto.keyword}, '%')
                    OR ovfbmi.link LIKE CONCAT('%', #{dto.keyword}, '%')
                    OR ovfbmi.get_code LIKE CONCAT('%', NULLIF(CAST(#{dto.keyword} AS UNSIGNED), 0), '%')
                    <if test="dto.backUserIds != null and dto.backUserIds.size() > 0 ">
                        OR ov.contact_id IN
                        <foreach collection="dto.backUserIds" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR ov.issue_id IN
                        <foreach collection="dto.backUserIds" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                )
            </if>

            <if test="dto.shootModelIds != null and dto.shootModelIds.size() > 0 ">
                AND ov.shoot_model_id IN
                <foreach collection="dto.shootModelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.getCode != null and dto.getCode != '' ">
                AND ovfbmi.get_code LIKE CONCAT('%', #{dto.getCode}, '%')
            </if>

            <if test="dto.getByIds != null and dto.getByIds.size() > 0 ">
                AND ovfbmi.get_by_id IN
                <foreach collection="dto.getByIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.getStatus != null">
                AND ovfbmi.get_status = #{dto.getStatus}
            </if>

            <if test="dto.getTimeBegin != null and dto.getTimeEnd != null">
                AND ovfbmi.get_time BETWEEN #{dto.getTimeBegin} AND #{dto.getTimeEnd}
            </if>

            <if test="dto.ids != null and dto.ids.size() > 0 ">
                AND ovfbmi.id IN
                <foreach collection="dto.ids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.editByIds != null and dto.editByIds.size() > 0 ">
                AND ovfbmi.edit_by_id IN
                <foreach collection="dto.editByIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.feedbackTimeBegin != null and dto.feedbackTimeEnd != null">
                AND ovfbmi.feedback_time BETWEEN #{dto.feedbackTimeBegin} AND #{dto.feedbackTimeEnd}
            </if>

        </where>
        <choose>
            <when test="dto.createTimeSort != null and dto.createTimeSort != ''">
                ORDER BY
                ovfbmi.create_time ${dto.createTimeSort}
            </when>
            <otherwise>
                ORDER BY
                max_status_time DESC,
                ovfbmi.status_time DESC
            </otherwise>
        </choose>
    </select>
    <select id="selectUnGetMaterialList" resultType="com.ruoyi.system.api.domain.vo.order.MaterialInfoListVO">
        SELECT
        ovfbmi.id,
        ov.id AS video_id,
        ov.rollback_id,
        ov.product_pic,
        ov.video_code,
        ov.product_chinese,
        ov.product_english,
        ov.pic_count,
        ov.shoot_model_id,
        ov.contact_id,
        ov.issue_id,
        ovfbmi.link,
        MAX(ovfbmi.status_time) OVER (PARTITION BY ov.id) AS max_status_time
        FROM
            order_video_feed_back_material_info ovfbmi
                JOIN order_video_feed_back_material ovfbm ON ovfbm.id = ovfbmi.material_id
                JOIN order_video ov ON ov.id = ovfbm.video_id
                LEFT JOIN (
                SELECT
                    m.video_id,
                    COUNT(*) AS history_clip_record
                FROM
                    order_video_feed_back_material_info t
                        JOIN order_video_feed_back_material m ON m.id = t.material_id
                GROUP BY
                    m.video_id
            ) hcr ON hcr.video_id = ov.id
        <where>
        ovfbmi.create_time >= #{oldDataEndTime}
        and ovfbmi.get_status = ${@<EMAIL>}
        and ovfbmi.status = ${@com.ruoyi.common.core.enums.MaterialInfoStatusEnum@TO_BE_DOWNLOADED.getCode}
        </where>
        ORDER BY
        max_status_time DESC,
        ovfbmi.status_time DESC
    </select>
    <select id="getMaterialInfoStatistics"
            resultType="com.ruoyi.system.api.domain.vo.order.GetMaterialInfoStatisticsVO">
        SELECT
            SUM( CASE WHEN STATUS = ${@com.ruoyi.common.core.enums.MaterialInfoStatusEnum@TO_BE_DOWNLOADED.getCode} THEN 1 ELSE 0 END ) AS total_feedback_count,
            SUM( CASE WHEN DATE ( enter_the_download_time ) = CURDATE() THEN 1 ELSE 0 END ) AS today_feedback_count,
            SUM( CASE WHEN STATUS = ${@com.ruoyi.common.core.enums.MaterialInfoStatusEnum@TO_BE_DOWNLOADED.getCode} AND get_status = ${@<EMAIL>} THEN 1 ELSE 0 END ) AS get_count,
            SUM( CASE WHEN STATUS = ${@com.ruoyi.common.core.enums.MaterialInfoStatusEnum@TO_BE_DOWNLOADED.getCode} AND get_status = ${@<EMAIL>} AND download_flag = ${@<EMAIL>} THEN 1 ELSE 0 END ) AS download_count,
            SUM( CASE WHEN download_flag = ${@<EMAIL>} AND DATE ( download_flag_time ) = CURDATE() THEN 1 ELSE 0 END ) AS today_download_count
        FROM
            order_video_feed_back_material_info
        WHERE
            create_time >= #{oldDataEndTime}
    </select>

    <select id="selectHistoryEditList"
            resultType="com.ruoyi.system.api.domain.vo.order.SelectHistoryEditListVO">
        SELECT
            ovfbmi.id,
            ovfbm.rollback_id,
            ovfbmi.user_id AS upload_by_id,
            ovfbmi.upload_time,
            ovfbmi.get_code,
            ovfbmi.link,
            ovfbmi.get_by_id,
            ovfbmi.get_time,
            ovfbmi.edit_by_id,
            ovfbmi.edit_time,
            ovfbmi.feedback_by_id,
            ovfbmi.feedback_time,
            ovfbmi.feedback_status,
            ovfbmi.feedback_remark
        FROM
            order_video_feed_back_material_info ovfbmi
                JOIN order_video_feed_back_material ovfbm ON ovfbm.id = ovfbmi.material_id
        WHERE ovfbm.video_id = #{videoId}
        GROUP BY
            ovfbmi.id,
            ovfbm.rollback_id,
            ovfbmi.user_id,
            ovfbmi.upload_time,
            ovfbmi.get_code,
            ovfbmi.link,
            ovfbmi.get_by_id,
            ovfbmi.get_time,
            ovfbmi.edit_by_id,
            ovfbmi.edit_time,
            ovfbmi.feedback_by_id,
            ovfbmi.feedback_time,
            ovfbmi.feedback_status,
            ovfbmi.feedback_remark
        ORDER BY ovfbmi.upload_time DESC
    </select>

    <select id="getTodayMaxGetCode" resultType="java.lang.Integer">
        SELECT
            IFNULL( MAX( ovfbmi.get_code ), 0 )
        FROM
            order_video_feed_back_material_info ovfbmi
        WHERE
            DATE ( ovfbmi.get_time ) = CURDATE()
    </select>

    <select id="checkDownload" resultType="java.lang.Boolean">
        SELECT
            CASE WHEN COUNT(1) > 0 THEN TRUE ELSE FALSE END
        FROM
            order_video_feed_back_material_info ovfbmi
                JOIN order_video_feed_back_material ovfbm ON ovfbm.id = ovfbmi.material_id
        WHERE
            ovfbm.video_id = #{videoId}
          AND ovfbmi.STATUS IN ( ${@com.ruoyi.common.core.enums.MaterialInfoStatusEnum@TO_BE_EDITED.getCode},${@com.ruoyi.common.core.enums.MaterialInfoStatusEnum@WAIT_FOR_FEEDBACK.getCode} )
    </select>

    <select id="getHistoryClipRecord" resultType="com.ruoyi.system.api.domain.vo.order.HistoryClipRecordListVO">
        SELECT
            rollback_id,
            submit_by_id,
            submit_time,
            task_type,
            after_sale_class,
            after_sale_video_type,
            after_sale_pic_type,
            work_order_type,
            is_initial,
            content,
            issue_pic AS issue_pic_id
        FROM
            (
                SELECT
                    ovtd.rollback_id,
                    ovtd.submit_by_id,
                    ovtd.submit_time,
                    ovt.task_type,
                    ovtd.after_sale_class,
                    ovtd.after_sale_video_type,
                    ovtd.after_sale_pic_type,
                    ovtd.work_order_type,
                    0 AS is_initial,
                    CASE
                        WHEN ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@AFTER_SALE.getCode} THEN ovtd.clip_record
                        WHEN ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@WORK_ORDER.getCode} THEN ovtd.content
                        ELSE ovtd.content
                        END AS content,
                    ovtd.issue_pic
                FROM
                    order_video_task_detail ovtd
                        JOIN order_video_task ovt ON ovt.id = ovtd.task_id
                WHERE
                    ovt.video_id = #{videoId}
                  AND ( ovtd.after_sale_video_type = ${@com.ruoyi.common.core.enums.OrderTaskAfterSaleVideoTypeEnum@RESHOOT_VIDEO.getCode}
                            OR ovtd.after_sale_video_type = ${@com.ruoyi.common.core.enums.OrderTaskAfterSaleVideoTypeEnum@RESHOT_VIDEO.getCode}
                            OR ovtd.after_sale_pic_type = ${@com.ruoyi.common.core.enums.OrderTaskAfterSalePicTypeEnum@RESHOOT_PIC.getCode}
                            OR ovtd.after_sale_pic_type = ${@com.ruoyi.common.core.enums.OrderTaskAfterSalePicTypeEnum@RESHOT_PIC.getCode}
                            OR ovtd.work_order_type = ${@com.ruoyi.common.core.enums.OrderTaskWorkOrderTypeEnum@NEED_CLIPS.getCode}
                      )
                  AND (
                          (ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@AFTER_SALE.getCode} AND ovtd.clip_record IS NOT NULL AND ovtd.clip_record != '')
                          OR
                          (ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@WORK_ORDER.getCode} AND ovtd.content IS NOT NULL AND ovtd.content != '')
                      )

                UNION ALL

                SELECT
                    NULL AS rollback_id,
                    ovc.submit_by_id,
                    ovc.submit_time,
                    NULL AS task_type,
                    NULL AS after_sale_class,
                    NULL AS after_sale_video_type,
                    NULL AS after_sale_pic_type,
                    NULL AS work_order_type,
                    1 AS is_initial,
                    GROUP_CONCAT(ovc.content) AS content,
                    NULL AS issue_pic
                FROM order_video_content ovc
                WHERE ovc.video_id = #{videoId} AND ovc.type = ${@com.ruoyi.common.core.enums.VideoContentTypeEnum@CLIPS_REQUIRED.getCode}
                GROUP BY
                    ovc.video_id,
                    ovc.submit_by_id,
                    ovc.submit_time
            )history_clip_record
        ORDER BY
            is_initial ASC,
            submit_time DESC
    </select>

    <select id="checkScored" resultType="java.lang.Boolean">
        SELECT
            CASE WHEN SUM(score_count) > 0 THEN TRUE ELSE FALSE END
        FROM (
                 SELECT
                     video_id,
                     SUM(CASE WHEN video_score IS NOT NULL THEN 1 ELSE 0 END) AS score_count
                 FROM order_video_feed_back
                 WHERE
                     video_id = #{videoId}
                     <if test="rollbackId != null">
                         AND rollback_id = #{rollbackId}
                     </if>

                 UNION ALL

                 SELECT
                     ovfbm.video_id,
                     SUM(CASE WHEN ovfbmi.video_score IS NOT NULL THEN 1 ELSE 0 END) AS score_count
                 FROM order_video_feed_back_material ovfbm
                          JOIN order_video_feed_back_material_info ovfbmi ON ovfbm.id = ovfbmi.material_id
                 WHERE
                     ovfbm.video_id = #{videoId}
                     <if test="rollbackId != null">
                         AND ovfbm.rollback_id = #{rollbackId}
                     </if>
             ) t
    </select>

    <select id="getExistClipRequireByVideoIds" resultType="java.lang.Long">
        SELECT
            DISTINCT( video_id )
        FROM
            (
                SELECT
                    ovt.video_id
                FROM
                    order_video_task_detail ovtd
                        JOIN order_video_task ovt ON ovt.id = ovtd.task_id
                WHERE
                   ( ovtd.after_sale_video_type = ${@com.ruoyi.common.core.enums.OrderTaskAfterSaleVideoTypeEnum@RESHOOT_VIDEO.getCode} OR ovtd.work_order_type = ${@com.ruoyi.common.core.enums.OrderTaskWorkOrderTypeEnum@NEED_CLIPS.getCode} )
                    <if test="videoIds != null and videoIds.size() > 0 ">
                        AND ovt.video_id IN
                        <foreach collection="videoIds" item="videoId" open="(" separator="," close=")">
                            #{videoId}
                        </foreach>
                    </if>

                UNION ALL

                SELECT
                    ovc.video_id
                FROM
                    order_video_content ovc
                WHERE
                    ovc.type = ${@com.ruoyi.common.core.enums.VideoContentTypeEnum@CLIPS_REQUIRED.getCode}
                    <if test="videoIds != null and videoIds.size() > 0 ">
                        AND ovc.video_id IN
                        <foreach collection="videoIds" item="videoId" open="(" separator="," close=")">
                            #{videoId}
                        </foreach>
                    </if>
            ) history_clip_record
    </select>

    <select id="getShootModelId" resultType="java.lang.Long">
        SELECT
            ov.shoot_model_id
        FROM
            order_video_feed_back_material_info ovfbmi
                JOIN order_video_feed_back_material ovfbm ON ovfbm.id = ovfbmi.material_id
                JOIN order_video ov ON ov.id = ovfbm.video_id
        WHERE
            ovfbmi.create_time >= #{oldDataEndTime}
            AND ovfbmi.status = #{status}
    </select>

    <select id="getReceivePerson" resultType="java.lang.Long">
        SELECT
            ovfbmi.get_by_id
        FROM
            order_video_feed_back_material_info ovfbmi
        WHERE
            ovfbmi.status = ${@com.ruoyi.common.core.enums.MaterialInfoStatusEnum@TO_BE_DOWNLOADED.getCode}
    </select>

    <select id="getEditPerson" resultType="java.lang.Long">
        SELECT
            ovfbmi.edit_by_id
        FROM
            order_video_feed_back_material_info ovfbmi
        WHERE
            ovfbmi.status = #{status}
    </select>

    <select id="selectClosedListByCondition" resultType="com.ruoyi.system.api.domain.vo.order.ClosedListVO">
        SELECT
            ct.id,
            ct.video_id,
            ct.type,
            ov.product_pic,
            ov.video_code,
            ov.product_chinese,
            ov.product_english,
            ov.product_link,
            ov.platform,
            ov.model_type,
            ov.shooting_country,
            ov.video_format,
            ov.pic_count,
            ov.reference_pic AS reference_pic_id,
            ov.refund_pic_count,
            IF(ct.type = 1, ovmc.model_id, ov.shoot_model_id) AS shoot_model_id,
            ov.contact_id,
            ov.issue_id,
            ct.close_reason,
            ct.close_by_id,
            ct.status_time AS close_time,
            ct.close_by_type,
            IFNULL( hcr.history_clip_record, 0 ) AS history_clip_record,
            IF( ct.type = 2 , IFNULL( MAX( ovulr.count ), 0 ) , 0 ) AS count
        FROM
            (
                SELECT
                    ovfbmi.id,
                    ovfbm.video_id,
                    ovfbm.rollback_id,
                    1 AS type,
                    ovfbmi.close_reason,
                    ovfbmi.close_by_id,
                    ovfbmi.status_time,
                    ovfbmi.close_by_type
                FROM
                    order_video_feed_back_material_info ovfbmi
                        JOIN order_video_feed_back_material ovfbm ON ovfbm.id = ovfbmi.material_id
                WHERE
                    ovfbmi.`status` = ${@<EMAIL>}

                UNION ALL

                SELECT
                    ovul.id,
                    ovul.video_id,
                    NULL AS rollback_id,
                    2 AS type,
                    ovul.close_reason,
                    ovul.close_by_id,
                    ovul.status_time,
                    NULL AS close_by_type
                FROM
                    order_video_upload_link ovul
                WHERE
                    ovul.`status` = ${@com.ruoyi.common.core.enums.UploadLinkStatusEnum@CANCEL_UPLOAD.getCode}
            ) ct
                JOIN order_video ov ON ov.id = ct.video_id
                LEFT JOIN (
                    SELECT
                        video_id,
                        rollback_id,
                        model_id
                    FROM
                        (
                        SELECT
                            video_id,
                            rollback_id,
                            model_id,
                            ROW_NUMBER() OVER ( PARTITION BY video_id, IFNULL( rollback_id, 0 ) ORDER BY selected_time DESC ) AS rn
                        FROM
                            order_video_model_change
                        WHERE
                            source = ${@com.ruoyi.common.core.enums.OrderVideoModelChangeSourceEnum@SHOOT_MODEL.getCode}
                        ) t
                    WHERE
                        rn = 1
                ) ovmc ON ovmc.video_id = ct.video_id AND IFNULL( ovmc.rollback_id, 0 ) = IFNULL( ct.rollback_id, 0 )
                LEFT JOIN (
                SELECT
                    m.video_id,
                    COUNT(*) AS history_clip_record
                FROM
                    order_video_feed_back_material_info t
                        JOIN order_video_feed_back_material m ON m.id = t.material_id
                GROUP BY
                    m.video_id
            ) hcr ON hcr.video_id = ct.video_id
                LEFT JOIN order_video_upload_link_record ovulr ON ovulr.upload_link_id = ct.id
        <where>

            <if test="dto.keyword != null and dto.keyword != '' ">
                AND (
                ov.video_code LIKE CONCAT('%', #{dto.keyword}, '%')
                OR ov.product_chinese LIKE CONCAT('%', #{dto.keyword}, '%')
                OR ov.product_english LIKE CONCAT('%', #{dto.keyword}, '%')
                OR ov.product_link LIKE CONCAT('%', #{dto.keyword}, '%')

                <if test="dto.backUserIds != null and dto.backUserIds.size() > 0 ">
                    OR ov.contact_id IN
                    <foreach collection="dto.backUserIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR ov.issue_id IN
                    <foreach collection="dto.backUserIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

                <if test="dto.modelIds != null and dto.modelIds.size() > 0 ">
                    OR ov.shoot_model_id IN
                    <foreach collection="dto.modelIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                )
            </if>

            <if test="dto.closeReason != null and dto.closeReason.size() > 0">
                AND ct.close_reason IN
                <foreach collection="dto.closeReason" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.closeTimeBegin != null and dto.closeTimeEnd != null">
                AND ct.status_time BETWEEN #{dto.closeTimeBegin} AND #{dto.closeTimeEnd}
            </if>

        </where>
        GROUP BY
            ct.id,
            ct.video_id,
            ct.type,
            ov.product_pic,
            ov.video_code,
            ov.product_chinese,
            ov.product_english,
            ov.product_link,
            ov.platform,
            ov.model_type,
            ov.shooting_country,
            ov.video_format,
            ov.pic_count,
            ov.reference_pic,
            ovmc.model_id,
            ov.shoot_model_id,
            ov.contact_id,
            ov.issue_id,
            ct.close_reason,
            ct.close_by_id,
            ct.status_time,
            ct.close_by_type
    </select>
    <select id="getInTheEditingVideoIds" resultType="java.lang.Long">
        SELECT
            DISTINCT ( video_id )
        FROM
            (
                SELECT
                    ovfbm.video_id
                FROM
                    order_video_feed_back_material_info ovfbmi
                        JOIN order_video_feed_back_material ovfbm ON ovfbm.id = ovfbmi.material_id
                WHERE
                    ovfbmi.`status` IN (
                                        ${@com.ruoyi.common.core.enums.MaterialInfoStatusEnum@TO_BE_DOWNLOADED.getCode},
                                        ${@com.ruoyi.common.core.enums.MaterialInfoStatusEnum@TO_BE_EDITED.getCode},
                                        ${@com.ruoyi.common.core.enums.MaterialInfoStatusEnum@WAIT_FOR_FEEDBACK.getCode}
#                                         ${@com.ruoyi.common.core.enums.MaterialInfoStatusEnum@NEED_TO_CONFIRM.getCode}
                                       )

#                 UNION ALL
#
#                 SELECT
#                     video_id
#                 FROM
#                     order_video_upload_link
#                 WHERE
#                     `status` IN (
#                                  ${@com.ruoyi.common.core.enums.UploadLinkStatusEnum@HAVEN_T_UPLOADED.getCode},
#                                  ${@com.ruoyi.common.core.enums.UploadLinkStatusEnum@UPLOAD_TO_BE_CONFIRMED.getCode}
#                                 )
            ) ite
        <if test="videoIds != null and videoIds.size() > 0">
            WHERE ite.video_id IN
            <foreach collection="videoIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getWorkbenchStatistics" resultType="com.ruoyi.system.api.domain.vo.order.WorkbenchVO">
        select
            SUM( CASE WHEN status = ${@com.ruoyi.common.core.enums.MaterialInfoStatusEnum@TO_BE_DOWNLOADED.getCode} THEN 1 ELSE 0 END ) AS downloadCount,
            SUM( CASE WHEN status = ${@com.ruoyi.common.core.enums.MaterialInfoStatusEnum@TO_BE_EDITED.getCode} THEN 1 ELSE 0 END ) AS toBeEditedCount,
            SUM( CASE WHEN status = ${@com.ruoyi.common.core.enums.MaterialInfoStatusEnum@WAIT_FOR_FEEDBACK.getCode} THEN 1 ELSE 0 END ) AS waitForFeedbackCount
        from order_video_feed_back_material_info ovfbmi
        where ovfbmi.create_time >= #{oldDataEndTime}
    </select>
    <select id="selectExportNeedConfirmList"
            resultType="com.ruoyi.system.api.domain.vo.order.ExportNeedConfirmListVO">
        WITH video_url_cte AS (
            SELECT
                video_id,
                rollback_id,
                video_url,
                create_time
            FROM
                (
                    SELECT
                        video_id,
                        rollback_id,
                        video_url,
                        create_time,
                        ROW_NUMBER() OVER ( PARTITION BY video_id ORDER BY create_time DESC ) AS rn
                    FROM
                        order_video_feed_back
                    WHERE
                        video_url IS NOT NULL
                      AND video_url != ''
                ) t
            WHERE
                t.rn = 1
        ),
             pic_url_cte AS (
                 SELECT
                     video_id,
                     rollback_id,
                     pic_url,
                     create_time
                 FROM
                     (
                         SELECT
                             video_id,
                             rollback_id,
                             pic_url,
                             create_time,
                             ROW_NUMBER() OVER ( PARTITION BY video_id ORDER BY create_time DESC ) AS rn
                         FROM
                             order_video_feed_back
                         WHERE
                             pic_url IS NOT NULL
                           AND pic_url != ''
                     ) t
                 WHERE
                     t.rn = 1
             ),
             latest_fb AS (
                 SELECT
                     video_id,
                     MAX(create_time) AS create_time
                 FROM
                     order_video_feed_back
                 GROUP BY
                     video_id
             )
        SELECT
            ov.video_code,
            ov.product_chinese,
            ov.product_english,
            ov.product_link,
            ov.create_order_user_name,
            ov.create_order_user_nick_name,
            vuc.video_url,
            puc.pic_url,
            ov.shooting_country,
            ov.shoot_model_id,
            fb.create_time
        FROM
            latest_fb fb
                JOIN order_video ov ON ov.id = fb.video_id
                LEFT JOIN video_url_cte vuc
                          ON ov.id = vuc.video_id
                              AND ov.rollback_id &lt;=&gt; vuc.rollback_id
                LEFT JOIN pic_url_cte puc
                          ON ov.id = puc.video_id
                              AND ov.rollback_id &lt;=&gt; puc.rollback_id
        WHERE fb.create_time BETWEEN #{feedbackTimeBegin} AND #{feedbackTimeEnd}
                AND ov.status IN (${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getCode},
                                  ${@<EMAIL>})
        ORDER BY fb.create_time DESC
     </select>
</mapper>